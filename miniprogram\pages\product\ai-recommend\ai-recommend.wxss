/**
 * AI推荐产品页面样式
 * AI Recommend Product Page Styles
 * 基于问题创建页面样式，移除投票相关功能，专注于AI推荐体验
 */

/* ==================== 页面布局 Page Layout ==================== */
.page-container {
  padding: 0 0 120rpx 0;
  background-color: var(--bg-color-secondary, #f5f5f5);
  min-height: 100vh;
  box-sizing: border-box;
}

.page-header {
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f8ff 100%);
  position: relative;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color-primary, #333);
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
}

.form-container {
  padding: 30rpx;
}

/* ==================== 分步式容器样式 Step Container ==================== */
.step-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.step-container:hover {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 步骤头部 */
.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f8f9fa;
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, var(--primary-color, #3B7ADB) 0%, #5A67D8 100%);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(59, 122, 219, 0.3);
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
  display: block;
  margin-bottom: 6rpx;
}

.step-desc {
  font-size: 26rpx;
  color: var(--text-color-secondary, #666);
  line-height: 1.4;
}

/* 步骤特殊样式 */
.step-1 {
  border-left: 4rpx solid #ff6b6b;
}

.step-2 {
  border-left: 4rpx solid #4ecdc4;
}

.step-3 {
  border-left: 4rpx solid #45b7d1;
}

.step-1 .step-number {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.step-2 .step-number {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.step-3 .step-number {
  background: linear-gradient(135deg, #45b7d1 0%, #3a9bc1 100%);
}

/* ==================== 表单元素样式 Form Elements ==================== */
.form-item {
  margin-bottom: 32rpx;
}

.form-item.primary {
  margin-bottom: 36rpx;
}

.form-item.secondary {
  margin-bottom: 28rpx;
}

.form-label {
  font-size: 28rpx;
  color: var(--text-color-primary, #333);
  margin-bottom: 16rpx;
  display: block;
  font-weight: 500;
  position: relative;
}

.form-item.primary .form-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
}

.form-item.secondary .form-label {
  font-size: 26rpx;
  color: #5a6c7d;
}

.required-tip {
  color: #ff4757;
  font-size: 22rpx;
  margin-left: 6rpx;
  font-weight: 600;
}

.title-input {
  width: 100%;
  height: 96rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  background-color: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.title-input:focus {
  border-color: #ff6b6b;
  background-color: #fff;
  box-shadow: 0 0 0 6rpx rgba(255, 107, 107, 0.1);
  transform: translateY(-2rpx);
}

.scene-input, .factor-input {
  width: 100%;
  height: 160rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 26rpx;
  box-sizing: border-box;
  background-color: #fff;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.scene-input:focus, .factor-input:focus {
  border-color: #45b7d1;
  box-shadow: 0 0 0 6rpx rgba(69, 183, 209, 0.1);
  transform: translateY(-2rpx);
}

.input-counter {
  font-size: 22rpx;
  color: #999;
  text-align: right;
  display: block;
  margin-top: 12rpx;
  font-weight: 500;
}

/* ==================== 标签组件样式 Tag Components ==================== */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.tag-item {
  padding: 16rpx 32rpx;
  background-color: #f8f9fa;
  border-radius: 36rpx;
  font-size: 28rpx;
  color: #5a6c7d;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  font-weight: 500;
}

.tag-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.tag-hover {
  opacity: 0.9;
  transform: scale(0.98);
}

.tag-item.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: #fff !important;
  border-color: #ff6b6b !important;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.25);
  transform: translateY(-2rpx);
}

.tag-check {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}

/* ==================== 预算区间样式 Budget Range ==================== */
.budget-container {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s ease;
}

.budget-container:focus-within {
  border-color: #45b7d1;
  box-shadow: 0 0 0 6rpx rgba(69, 183, 209, 0.1);
}

.budget-input {
  width: 180rpx;
  height: 72rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  box-sizing: border-box;
  background-color: #fff;
  transition: all 0.3s ease;
  text-align: center;
}

.budget-input:focus {
  border-color: #45b7d1;
  box-shadow: 0 0 0 4rpx rgba(69, 183, 209, 0.1);
}

.budget-separator {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: #5a6c7d;
  font-weight: 500;
}

.budget-currency {
  margin-left: 20rpx;
  font-size: 26rpx;
  color: #5a6c7d;
  font-weight: 500;
}

/* ==================== 通用提示文本样式 ==================== */
.tip-text {
  font-size: 24rpx;
  color: var(--text-color-placeholder, #999);
  margin-top: 10rpx;
}

/* ==================== 品牌选择区域样式 ==================== */
.selected-category-display {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  border-radius: 16rpx;
  margin-bottom: 28rpx;
  box-shadow: 0 2rpx 12rpx rgba(78, 205, 196, 0.2);
}

.category-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.category-name {
  font-size: 30rpx;
  color: #fff;
  font-weight: 600;
  margin-right: 16rpx;
}

.category-count {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.brand-selection-area {
  margin-top: 24rpx;
}

.brand-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.brand-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18rpx 28rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 36rpx;
  background-color: #fff;
  transition: all 0.3s ease;
  min-width: 120rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.brand-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.brand-item.selected {
  border-color: #4ecdc4;
  background: linear-gradient(135deg, #f0fdfc 0%, #e6fffe 100%);
  box-shadow: 0 4rpx 16rpx rgba(78, 205, 196, 0.15);
}

.brand-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 8rpx;
  font-weight: 500;
}

.brand-item.selected .brand-text {
  color: #4ecdc4;
  font-weight: 600;
}

.brand-check {
  font-size: 20rpx;
  color: #4ecdc4;
  font-weight: bold;
}

.brand-hover {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 已选择品牌摘要 */
.selected-brands-summary {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f0fdfc 0%, #e6fffe 100%);
  border-radius: 16rpx;
  border: 1rpx solid #4ecdc4;
  margin-bottom: 20rpx;
}

.summary-text {
  font-size: 26rpx;
  color: #4ecdc4;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}

.summary-brands {
  font-size: 24rpx;
  color: #5a6c7d;
  line-height: 1.4;
}

/* 品牌提示 */
.brand-tip {
  display: flex;
  align-items: flex-start;
  padding: 18rpx 22rpx;
  background: #f8fbff;
  border-radius: 14rpx;
  border-left: 4rpx solid #91d5ff;
}

.brand-tip .tip-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.brand-tip .tip-text {
  flex: 1;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  margin-top: 0;
}

/* ==================== AI推荐结果展示样式 ==================== */
.products-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-item {
  background-color: #f8fcff;
  border: 1rpx solid #e6f7ff;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
}

.product-item:hover {
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
}

.product-info {
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: block;
}

/* ==================== AI推荐理由展示样式 ==================== */
.ai-recommend-reason {
  background-color: #f8fcff;
  border: 1rpx solid #e6f7ff;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-top: 12rpx;
}

.reason-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.ai-tag {
  color: #1890ff;
  font-size: 22rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.expand-btn {
  color: #1890ff;
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  background-color: rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.expand-btn:active {
  opacity: 0.7;
  transform: scale(0.95);
}

.reason-content {
  transition: all 0.3s ease;
  overflow: hidden;
}

.reason-content.collapsed {
  max-height: 60rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reason-content.expanded {
  max-height: none;
}

.recommend-reason {
  margin-bottom: 12rpx;
}

.reason-text {
  color: #595959;
  line-height: 1.5;
  font-size: 24rpx;
}

.highlights-section {
  margin-top: 8rpx;
}

.highlights-title {
  color: #262626;
  font-weight: 500;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  display: block;
}

.highlights-list {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.highlight-item {
  color: #595959;
  font-size: 22rpx;
  line-height: 1.4;
  padding-left: 8rpx;
}

/* ==================== 产品操作按钮样式 ==================== */
.product-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  padding: 0 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  border: none;
  gap: 6rpx;
}

.compare-btn {
  background-color: #f0f9ff;
  color: #1890ff;
  border: 1rpx solid #91d5ff;
}

.compare-btn:active {
  background-color: #e6f7ff;
  transform: scale(0.95);
}

.compare-btn.in-compare {
  background-color: #fff0f0;
  color: #f5222d;
  border-color: #ffa39e;
}

.detail-btn {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1rpx solid #b7eb8f;
}

.detail-btn:active {
  background-color: #f6ffed;
  transform: scale(0.95);
}

.btn-icon {
  font-size: 20rpx;
}

/* ==================== 获取推荐按钮样式 ==================== */
.recommend-container {
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  gap: 20rpx;
}

.recommend-btn {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, var(--primary-color, #3B7ADB) 0%, #5A67D8 100%);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.25);
  gap: 8rpx;
}

.recommend-btn:active {
  background: linear-gradient(135deg, #2E63B8 0%, #4C51BF 100%);
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(59, 122, 219, 0.3);
}

.recommend-btn.disabled {
  background-color: #ccc !important;
  transform: none !important;
  box-shadow: none !important;
  pointer-events: none;
}

.retry-btn {
  width: 120rpx;
  height: 88rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #ddd;
  transition: all 0.3s ease;
  gap: 4rpx;
}

.retry-btn:active {
  background-color: #eaeaea;
  transform: scale(0.98);
}

/* ==================== 加载提示样式 ==================== */
.page-container .loading-overlay {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: auto !important;
  background-color: transparent !important;
  z-index: 1000 !important;
  display: flex !important;
  align-items: flex-end !important;
  justify-content: center !important;
  padding: 0 30rpx 150rpx 30rpx !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.3s ease !important;
  pointer-events: none !important;
}

.page-container .loading-overlay.show {
  opacity: 1 !important;
  visibility: visible !important;
}

.page-container .loading-content {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f8ff 100%) !important;
  border: 1rpx solid #e1e8ff !important;
  border-radius: 20rpx !important;
  padding: 32rpx 40rpx !important;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  max-width: 600rpx !important;
  min-width: 480rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(59, 122, 219, 0.15), 0 2rpx 8rpx rgba(0, 0, 0, 0.08) !important;
  transform: translateY(-20rpx) !important;
  animation: slideUpGentle 0.4s ease-out forwards !important;
}

@keyframes slideUpGentle {
  0% {
    transform: translateY(60rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(-20rpx);
    opacity: 1;
  }
}

.page-container .loading-spinner {
  width: 48rpx !important;
  height: 48rpx !important;
  border: 3rpx solid rgba(59, 122, 219, 0.2) !important;
  border-top: 3rpx solid #3B7ADB !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  margin-right: 24rpx !important;
  margin-bottom: 0 !important;
  flex-shrink: 0 !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.page-container .loading-text-container {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
}

.page-container .loading-text {
  font-size: 28rpx !important;
  color: #3B7ADB !important;
  margin-bottom: 4rpx !important;
  font-weight: 600 !important;
  text-align: left !important;
  line-height: 1.2 !important;
}

.page-container .loading-tip {
  font-size: 24rpx !important;
  color: #5a6c7d !important;
  text-align: left !important;
  line-height: 1.3 !important;
  opacity: 0.8 !important;
  margin-top: 4rpx !important;
}

/* ==================== 响应式调整 ==================== */
@media (max-width: 750rpx) {
  .product-actions {
    flex-direction: column;
    gap: 12rpx;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .recommend-container {
    flex-direction: column;
    gap: 12rpx;
  }

  .retry-btn {
    width: 100%;
  }

  .modal-content {
    max-height: 85vh;
  }

  .modal-body {
    max-height: 65vh;
  }

  .brand-item {
    min-width: 100rpx;
    padding: 12rpx 20rpx;
  }

  .brand-text {
    font-size: 24rpx;
  }
}